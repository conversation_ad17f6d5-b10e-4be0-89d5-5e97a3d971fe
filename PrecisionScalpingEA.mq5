//+------------------------------------------------------------------+
//|                                        SmartMoneyConceptsEA.mq5 |
//|                                    Copyright 2025, InstaScan Dev |
//|                   Smart Money Concepts EA - Institutional Logic  |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, InstaScan Dev"
#property link      ""
#property version   "2.00"
#property description "Smart Money Concepts EA with Order Blocks, CHoCH, Liquidity Sweeps for $50 account scalping"

//--- Input parameters
input group "=== Risk Management ==="
input double   Risk_Percent = 0.5;              // Risk percentage per trade (0.5-1% for $50 account)
input int      Stop_Loss_Pips = 5;              // Stop Loss in pips (tight SL)
input double   RR_Ratio_1 = 1.0;                // Risk:Reward ratio for TP1
input double   RR_Ratio_2 = 2.0;                // Risk:Reward ratio for TP2
input double   RR_Ratio_3 = 3.0;                // Risk:Reward ratio for TP3
input double   Daily_Profit_Target = 5.0;       // Daily profit target in USD ($5-8 for $50 account)
input double   Daily_Loss_Limit = 2.0;          // Daily loss limit in %
input int      Max_Daily_Trades = 10;           // Maximum trades per day
input int      Magic_Number = 789123;           // EA Magic Number

input group "=== Smart Money Concepts ==="
input ENUM_TIMEFRAMES HTF_Timeframe = PERIOD_H1;    // Higher timeframe for bias (1H/4H)
input ENUM_TIMEFRAMES LTF_Timeframe = PERIOD_M5;    // Lower timeframe for execution (1M/5M)
input int      Swing_Lookback = 20;             // Bars to look back for swing points
input double   Liquidity_Buffer_Pips = 2.0;     // Buffer above/below liquidity levels
input int      OB_Validity_Bars = 50;           // Order Block validity in bars
input int      FVG_Min_Size_Pips = 3;           // Minimum FVG size in pips
input bool     Use_Premium_Discount = true;     // Use 50% premium/discount zones
input bool     Require_CHoCH = true;            // Require Change of Character confirmation

input group "=== Trading Sessions ==="
input bool     Trade_London_Session = true;     // Trade London session (8:00-11:00 GMT)
input bool     Trade_NY_Session = true;         // Trade New York session (13:00-16:00 GMT)
input bool     Avoid_News = true;               // Avoid trading during high impact news
input double   Max_Spread_Pips = 2.0;           // Maximum spread in pips
input int      GMT_Offset = 0;                  // GMT offset for broker time

input group "=== Advanced Settings ==="
input bool     Use_Multi_TP = true;             // Use multiple take profit levels
input double   TP1_Close_Percent = 30.0;        // % to close at TP1
input double   TP2_Close_Percent = 40.0;        // % to close at TP2
input bool     Move_SL_to_BE = true;            // Move SL to breakeven after TP1
input bool     Use_Trailing_Stop = true;        // Use trailing stop after TP2
input int      Trail_Start_Pips = 10;           // Start trailing after X pips
input int      Trail_Step_Pips = 3;             // Trailing step in pips

input group "=== Frequency Management ==="
input bool     Increase_Frequency = true;       // Increase frequency after small profits
input double   Small_Profit_Threshold = 3.0;    // Small profit threshold in dollars

input group "=== Dashboard ==="
input bool     Show_Dashboard = true;           // Show trading dashboard
input int      Dashboard_X = 20;                // Dashboard X position
input int      Dashboard_Y = 50;                // Dashboard Y position
input color    Dashboard_Color = clrLimeGreen;  // Dashboard text color

//--- SMC Structure definitions
struct SwingPoint
{
   double price;
   datetime time;
   int bar_index;
   bool is_high;
};

struct OrderBlock
{
   double high;
   double low;
   datetime time;
   bool is_bullish;
   bool is_valid;
   int formation_bar;
};

struct FairValueGap
{
   double upper;
   double lower;
   datetime time;
   bool is_bullish;
   bool is_filled;
   int formation_bar;
};

struct LiquidityLevel
{
   double price;
   datetime time;
   bool is_swept;
   int touch_count;
   bool is_high;
};

//--- Global variables
double daily_profit = 0.0;
double daily_loss = 0.0;
int daily_trades = 0;
bool daily_target_reached = false;
bool daily_loss_exceeded = false;
datetime current_date = 0;
double pip_multiplier = 1.0;
double starting_balance = 0.0;

//--- Frequency management variables
int small_profit_count = 0;
double frequency_multiplier = 1.0;

// SMC variables
SwingPoint swing_highs[];
SwingPoint swing_lows[];
OrderBlock order_blocks[];
FairValueGap fair_value_gaps[];
LiquidityLevel liquidity_levels[];
int market_bias = 0; // 1 = bullish, -1 = bearish, 0 = neutral
double premium_discount_line = 0.0;
bool choch_confirmed = false;
datetime last_choch_time = 0;

//--- Trade management
#include <Trade\Trade.mqh>
CTrade trade;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Determine pip multiplier
    if(_Digits == 5 || _Digits == 3)
        pip_multiplier = 10.0;
    else
        pip_multiplier = 1.0;

    // Initialize trade object
    trade.SetExpertMagicNumber(Magic_Number);
    trade.SetDeviationInPoints(30);
    trade.SetTypeFilling(ORDER_FILLING_FOK);
    trade.SetTypeFillingBySymbol(_Symbol);

    // Initialize SMC arrays
    ArrayResize(swing_highs, 0);
    ArrayResize(swing_lows, 0);
    ArrayResize(order_blocks, 0);
    ArrayResize(fair_value_gaps, 0);
    ArrayResize(liquidity_levels, 0);

    // Initialize daily tracking
    starting_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    ResetDailyStats();

    Print("✓ Smart Money Concepts EA initialized successfully");
    Print("✓ Target: $", Daily_Profit_Target, " daily profit from $", starting_balance, " account");
    Print("✓ Strategy: SMC with Order Blocks, CHoCH, Liquidity Sweeps");
    Print("✓ HTF: ", EnumToString(HTF_Timeframe), " | LTF: ", EnumToString(LTF_Timeframe));
    Print("✓ Risk per trade: ", Risk_Percent, "% | SL: ", Stop_Loss_Pips, " pips");

    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Remove dashboard and chart objects
    RemoveDashboard();
    RemoveChartObjects();

    Print("✓ Smart Money Concepts EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    static datetime last_tick_debug = 0;
    static int tick_count = 0;
    tick_count++;

    // Debug every 100 ticks
    if(tick_count % 100 == 0)
    {
        Print("📊 Tick #", tick_count, " | Daily Trades: ", daily_trades, "/", Max_Daily_Trades,
              " | Profit: $", daily_profit, " | Session: ", IsWithinTradingSession());
    }

    // Check if new day started
    CheckNewDay();

    // Update daily statistics
    UpdateDailyStats();

    // Check daily limits
    if(daily_target_reached || daily_loss_exceeded || daily_trades >= Max_Daily_Trades)
    {
        if(Show_Dashboard) UpdateDashboard();
        return;
    }

    // Check trading session
    if(!IsWithinTradingSession())
    {
        if(Show_Dashboard) UpdateDashboard();
        return;
    }

    // Check spread
    if(!IsSpreadAcceptable())
    {
        static datetime last_spread_warning = 0;
        if(TimeCurrent() - last_spread_warning > 300) // Every 5 minutes
        {
            double spread_pips = (SymbolInfoDouble(_Symbol, SYMBOL_ASK) - SymbolInfoDouble(_Symbol, SYMBOL_BID)) / _Point / pip_multiplier;
            Print("⚠️ Spread too wide: ", spread_pips, " pips (max: ", Max_Spread_Pips, ")");
            last_spread_warning = TimeCurrent();
        }
        if(Show_Dashboard) UpdateDashboard();
        return;
    }

    // Update SMC analysis
    UpdateSMCAnalysis();

    // Check for SMC trading signals
    int signal = GetSMCTradingSignal();

    // If no SMC signal, try simple backup signal for testing
    if(signal == 0)
        signal = GetBackupTradingSignal();

    // Check if we already have open positions
    int open_positions = 0;
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionGetTicket(i) == 0) continue;
        if(PositionGetInteger(POSITION_MAGIC) != Magic_Number) continue;
        if(PositionGetString(POSITION_SYMBOL) != _Symbol) continue;
        open_positions++;
    }

    // Execute trades based on signal (only if no open positions)
    if(signal == 1 && open_positions == 0) // Buy signal
    {
        Print("🚀 Executing BUY trade...");
        ExecuteBuyTrade();
    }
    else if(signal == -1 && open_positions == 0) // Sell signal
    {
        Print("🚀 Executing SELL trade...");
        ExecuteSellTrade();
    }
    else if(signal != 0 && open_positions > 0)
    {
        Print("⏸️ Signal detected but ", open_positions, " position(s) already open");
    }

    // Manage existing trades
    ManageOpenTrades();

    // Update dashboard
    if(Show_Dashboard) UpdateDashboard();
}

//+------------------------------------------------------------------+
//| Check if new day started                                         |
//+------------------------------------------------------------------+
void CheckNewDay()
{
    datetime today = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));
    
    if(current_date != today)
    {
        current_date = today;
        ResetDailyStats();
        Print("✓ New trading day started - Statistics reset");
    }
}

//+------------------------------------------------------------------+
//| Reset daily statistics                                           |
//+------------------------------------------------------------------+
void ResetDailyStats()
{
    daily_profit = 0.0;
    daily_loss = 0.0;
    daily_trades = 0;
    daily_target_reached = false;
    daily_loss_exceeded = false;

    // Reset SMC analysis
    market_bias = 0;
    choch_confirmed = false;
    last_choch_time = 0;
    premium_discount_line = 0.0;
}

//+------------------------------------------------------------------+
//| Update daily statistics                                          |
//+------------------------------------------------------------------+
void UpdateDailyStats()
{
    daily_profit = 0.0;
    daily_loss = 0.0;
    daily_trades = 0;
    
    // Get today's deals
    datetime today_start = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));
    datetime today_end = today_start + 86400;
    
    if(!HistorySelect(today_start, today_end))
        return;
    
    int total_deals = HistoryDealsTotal();
    
    for(int i = 0; i < total_deals; i++)
    {
        ulong ticket = HistoryDealGetTicket(i);
        if(ticket == 0) continue;
        
        if(HistoryDealGetString(ticket, DEAL_SYMBOL) != _Symbol) continue;
        if(HistoryDealGetInteger(ticket, DEAL_MAGIC) != Magic_Number) continue;
        
        ENUM_DEAL_TYPE deal_type = (ENUM_DEAL_TYPE)HistoryDealGetInteger(ticket, DEAL_TYPE);
        if(deal_type != DEAL_TYPE_BUY && deal_type != DEAL_TYPE_SELL) continue;
        
        double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
        double swap = HistoryDealGetDouble(ticket, DEAL_SWAP);
        double commission = HistoryDealGetDouble(ticket, DEAL_COMMISSION);
        
        double total_profit = profit + swap + commission;
        
        if(total_profit > 0)
        {
            daily_profit += total_profit;
            
            // Check for small profits to increase frequency
            if(Increase_Frequency && total_profit <= Small_Profit_Threshold)
            {
                small_profit_count++;
                frequency_multiplier = 1.0 + (small_profit_count * 0.1); // Increase by 10% per small profit
                frequency_multiplier = MathMin(frequency_multiplier, 2.0); // Cap at 2x
            }
        }
        else
        {
            daily_loss += MathAbs(total_profit);
        }
        
        daily_trades++;
    }
    
    // Check daily limits
    if(daily_profit >= Daily_Profit_Target)
    {
        daily_target_reached = true;
        Print("🎯 DAILY PROFIT TARGET REACHED: $", daily_profit);
        CloseAllTrades();
    }
    
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double max_daily_loss = balance * Daily_Loss_Limit / 100.0;
    
    if(daily_loss >= max_daily_loss)
    {
        daily_loss_exceeded = true;
        Print("⚠️ DAILY LOSS LIMIT EXCEEDED: $", daily_loss);
        CloseAllTrades();
    }
}

//+------------------------------------------------------------------+
//| Check if within trading session                                 |
//+------------------------------------------------------------------+
bool IsWithinTradingSession()
{
    // For testing purposes, make sessions more flexible
    if(!Trade_London_Session && !Trade_NY_Session)
        return true;

    MqlDateTime time_struct;
    TimeToStruct(TimeCurrent() + GMT_Offset * 3600, time_struct);

    int current_hour = time_struct.hour;

    // Extended London session: 7:00-12:00 GMT (more flexible)
    bool london_active = (current_hour >= 7 && current_hour <= 12);

    // Extended New York session: 12:00-17:00 GMT (more flexible)
    bool ny_active = (current_hour >= 12 && current_hour <= 17);

    bool session_active = false;
    if(Trade_London_Session && london_active) session_active = true;
    if(Trade_NY_Session && ny_active) session_active = true;

    // Debug print
    static int last_hour = -1;
    if(current_hour != last_hour)
    {
        Print("🕐 Current Hour: ", current_hour, " GMT | London: ", london_active, " | NY: ", ny_active, " | Active: ", session_active);
        last_hour = current_hour;
    }

    return session_active;
}

//+------------------------------------------------------------------+
//| Check if spread is acceptable                                   |
//+------------------------------------------------------------------+
bool IsSpreadAcceptable()
{
    double spread = (SymbolInfoDouble(_Symbol, SYMBOL_ASK) - SymbolInfoDouble(_Symbol, SYMBOL_BID)) / _Point;
    double spread_pips = spread / pip_multiplier;

    return (spread_pips <= Max_Spread_Pips);
}

//+------------------------------------------------------------------+
//| Update Smart Money Concepts Analysis                            |
//+------------------------------------------------------------------+
void UpdateSMCAnalysis()
{
    // Update market structure analysis
    UpdateMarketStructure();

    // Update order blocks
    UpdateOrderBlocks();

    // Update fair value gaps
    UpdateFairValueGaps();

    // Update liquidity levels
    UpdateLiquidityLevels();

    // Calculate premium/discount zones
    CalculatePremiumDiscount();

    // Check for Change of Character (CHoCH)
    CheckForCHoCH();
}

//+------------------------------------------------------------------+
//| Update Market Structure (Swing Highs and Lows)                 |
//+------------------------------------------------------------------+
void UpdateMarketStructure()
{
    double high[], low[];
    datetime time[];

    int bars_to_copy = Swing_Lookback + 10;

    if(CopyHigh(_Symbol, HTF_Timeframe, 0, bars_to_copy, high) < bars_to_copy ||
       CopyLow(_Symbol, HTF_Timeframe, 0, bars_to_copy, low) < bars_to_copy ||
       CopyTime(_Symbol, HTF_Timeframe, 0, bars_to_copy, time) < bars_to_copy)
        return;

    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(time, true);

    // Find swing highs
    for(int i = Swing_Lookback/2; i < bars_to_copy - Swing_Lookback/2; i++)
    {
        bool is_swing_high = true;
        for(int j = i - Swing_Lookback/2; j <= i + Swing_Lookback/2; j++)
        {
            if(j != i && high[j] >= high[i])
            {
                is_swing_high = false;
                break;
            }
        }

        if(is_swing_high)
        {
            SwingPoint swing;
            swing.price = high[i];
            swing.time = time[i];
            swing.bar_index = i;
            swing.is_high = true;

            AddSwingPoint(swing, true);
        }
    }

    // Find swing lows
    for(int i = Swing_Lookback/2; i < bars_to_copy - Swing_Lookback/2; i++)
    {
        bool is_swing_low = true;
        for(int j = i - Swing_Lookback/2; j <= i + Swing_Lookback/2; j++)
        {
            if(j != i && low[j] <= low[i])
            {
                is_swing_low = false;
                break;
            }
        }

        if(is_swing_low)
        {
            SwingPoint swing;
            swing.price = low[i];
            swing.time = time[i];
            swing.bar_index = i;
            swing.is_high = false;

            AddSwingPoint(swing, false);
        }
    }

    // Determine market bias based on structure
    DetermineMarketBias();
}

//+------------------------------------------------------------------+
//| Add swing point to array                                        |
//+------------------------------------------------------------------+
void AddSwingPoint(SwingPoint &swing, bool is_high)
{
    if(is_high)
    {
        // Check if this swing high already exists
        for(int i = 0; i < ArraySize(swing_highs); i++)
        {
            if(MathAbs(swing_highs[i].time - swing.time) < PeriodSeconds(HTF_Timeframe))
                return; // Already exists
        }

        // Add new swing high
        int size = ArraySize(swing_highs);
        ArrayResize(swing_highs, size + 1);
        swing_highs[size] = swing;

        // Keep only recent swing points
        if(size > 20)
        {
            for(int i = 0; i < size - 1; i++)
                swing_highs[i] = swing_highs[i + 1];
            ArrayResize(swing_highs, size);
        }
    }
    else
    {
        // Check if this swing low already exists
        for(int i = 0; i < ArraySize(swing_lows); i++)
        {
            if(MathAbs(swing_lows[i].time - swing.time) < PeriodSeconds(HTF_Timeframe))
                return; // Already exists
        }

        // Add new swing low
        int size = ArraySize(swing_lows);
        ArrayResize(swing_lows, size + 1);
        swing_lows[size] = swing;

        // Keep only recent swing points
        if(size > 20)
        {
            for(int i = 0; i < size - 1; i++)
                swing_lows[i] = swing_lows[i + 1];
            ArrayResize(swing_lows, size);
        }
    }
}

//+------------------------------------------------------------------+
//| Determine market bias based on structure                        |
//+------------------------------------------------------------------+
void DetermineMarketBias()
{
    int highs_count = ArraySize(swing_highs);
    int lows_count = ArraySize(swing_lows);

    if(highs_count < 2 || lows_count < 2)
    {
        market_bias = 0;
        return;
    }

    // Check for Higher Highs and Higher Lows (Bullish)
    bool higher_highs = (swing_highs[highs_count-1].price > swing_highs[highs_count-2].price);
    bool higher_lows = (swing_lows[lows_count-1].price > swing_lows[lows_count-2].price);

    // Check for Lower Highs and Lower Lows (Bearish)
    bool lower_highs = (swing_highs[highs_count-1].price < swing_highs[highs_count-2].price);
    bool lower_lows = (swing_lows[lows_count-1].price < swing_lows[lows_count-2].price);

    if(higher_highs && higher_lows)
        market_bias = 1; // Bullish
    else if(lower_highs && lower_lows)
        market_bias = -1; // Bearish
    else
        market_bias = 0; // Neutral/Ranging
}

//+------------------------------------------------------------------+
//| Update Order Blocks                                             |
//+------------------------------------------------------------------+
void UpdateOrderBlocks()
{
    double open[], high[], low[], close[];
    datetime time[];

    int bars_to_copy = 100;

    if(CopyOpen(_Symbol, LTF_Timeframe, 0, bars_to_copy, open) < bars_to_copy ||
       CopyHigh(_Symbol, LTF_Timeframe, 0, bars_to_copy, high) < bars_to_copy ||
       CopyLow(_Symbol, LTF_Timeframe, 0, bars_to_copy, low) < bars_to_copy ||
       CopyClose(_Symbol, LTF_Timeframe, 0, bars_to_copy, close) < bars_to_copy ||
       CopyTime(_Symbol, LTF_Timeframe, 0, bars_to_copy, time) < bars_to_copy)
        return;

    ArraySetAsSeries(open, true);
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);
    ArraySetAsSeries(time, true);

    // Look for displacement candles and create order blocks
    for(int i = 1; i < bars_to_copy - 1; i++)
    {
        double candle_size = MathAbs(close[i] - open[i]) / (_Point * pip_multiplier);
        double prev_candle_size = MathAbs(close[i+1] - open[i+1]) / (_Point * pip_multiplier);

        // Check for displacement (strong candle after weak candle)
        if(candle_size > prev_candle_size * 2.0 && candle_size > 5.0) // At least 5 pips displacement
        {
            OrderBlock ob;
            ob.time = time[i+1]; // Previous candle forms the order block
            ob.formation_bar = i+1;
            ob.is_valid = true;

            if(close[i] > open[i]) // Bullish displacement
            {
                ob.is_bullish = true;
                ob.high = high[i+1];
                ob.low = low[i+1];
            }
            else // Bearish displacement
            {
                ob.is_bullish = false;
                ob.high = high[i+1];
                ob.low = low[i+1];
            }

            AddOrderBlock(ob);
        }
    }

    // Clean up old/invalid order blocks
    CleanupOrderBlocks();
}

//+------------------------------------------------------------------+
//| Add Order Block to array                                        |
//+------------------------------------------------------------------+
void AddOrderBlock(OrderBlock &ob)
{
    // Check if similar order block already exists
    for(int i = 0; i < ArraySize(order_blocks); i++)
    {
        if(MathAbs(order_blocks[i].time - ob.time) < PeriodSeconds(LTF_Timeframe) * 3)
            return; // Similar OB already exists
    }

    int size = ArraySize(order_blocks);
    ArrayResize(order_blocks, size + 1);
    order_blocks[size] = ob;

    Print("📦 New Order Block: ", ob.is_bullish ? "BULLISH" : "BEARISH",
          " | High: ", ob.high, " | Low: ", ob.low);
}

//+------------------------------------------------------------------+
//| Update Fair Value Gaps                                          |
//+------------------------------------------------------------------+
void UpdateFairValueGaps()
{
    double high[], low[];
    datetime time[];

    int bars_to_copy = 50;

    if(CopyHigh(_Symbol, LTF_Timeframe, 0, bars_to_copy, high) < bars_to_copy ||
       CopyLow(_Symbol, LTF_Timeframe, 0, bars_to_copy, low) < bars_to_copy ||
       CopyTime(_Symbol, LTF_Timeframe, 0, bars_to_copy, time) < bars_to_copy)
        return;

    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(time, true);

    // Look for Fair Value Gaps (3-candle pattern)
    for(int i = 2; i < bars_to_copy - 1; i++)
    {
        // Bullish FVG: low[i-1] > high[i+1]
        if(low[i-1] > high[i+1])
        {
            double gap_size = (low[i-1] - high[i+1]) / (_Point * pip_multiplier);
            if(gap_size >= FVG_Min_Size_Pips)
            {
                FairValueGap fvg;
                fvg.upper = low[i-1];
                fvg.lower = high[i+1];
                fvg.time = time[i];
                fvg.is_bullish = true;
                fvg.is_filled = false;
                fvg.formation_bar = i;

                AddFairValueGap(fvg);
            }
        }

        // Bearish FVG: high[i-1] < low[i+1]
        if(high[i-1] < low[i+1])
        {
            double gap_size = (low[i+1] - high[i-1]) / (_Point * pip_multiplier);
            if(gap_size >= FVG_Min_Size_Pips)
            {
                FairValueGap fvg;
                fvg.upper = low[i+1];
                fvg.lower = high[i-1];
                fvg.time = time[i];
                fvg.is_bullish = false;
                fvg.is_filled = false;
                fvg.formation_bar = i;

                AddFairValueGap(fvg);
            }
        }
    }

    // Check if existing FVGs are filled
    CheckFVGFilled();
}

//+------------------------------------------------------------------+
//| Add Fair Value Gap to array                                     |
//+------------------------------------------------------------------+
void AddFairValueGap(FairValueGap &fvg)
{
    // Check if similar FVG already exists
    for(int i = 0; i < ArraySize(fair_value_gaps); i++)
    {
        if(MathAbs(fair_value_gaps[i].time - fvg.time) < PeriodSeconds(LTF_Timeframe) * 2)
            return; // Similar FVG already exists
    }

    int size = ArraySize(fair_value_gaps);
    ArrayResize(fair_value_gaps, size + 1);
    fair_value_gaps[size] = fvg;

    Print("⚡ New FVG: ", fvg.is_bullish ? "BULLISH" : "BEARISH",
          " | Upper: ", fvg.upper, " | Lower: ", fvg.lower);
}

//+------------------------------------------------------------------+
//| Get SMC Trading Signal                                          |
//+------------------------------------------------------------------+
int GetSMCTradingSignal()
{
    static datetime last_debug_time = 0;
    datetime current_time = TimeCurrent();

    // Debug print every 5 minutes
    if(current_time - last_debug_time >= 300)
    {
        Print("🔍 SMC Analysis - Bias: ", market_bias, " | OBs: ", ArraySize(order_blocks),
              " | FVGs: ", ArraySize(fair_value_gaps), " | Liquidity: ", ArraySize(liquidity_levels));
        last_debug_time = current_time;
    }

    double current_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);

    // Simplified signal generation for testing
    // First, try to establish market bias using simple price action
    if(market_bias == 0)
    {
        // Simple bias determination using recent price movement
        double high_5 = iHigh(_Symbol, LTF_Timeframe, 5);
        double low_5 = iLow(_Symbol, LTF_Timeframe, 5);
        double high_1 = iHigh(_Symbol, LTF_Timeframe, 1);
        double low_1 = iLow(_Symbol, LTF_Timeframe, 1);

        if(high_1 > high_5 && low_1 > low_5)
        {
            market_bias = 1; // Bullish
            Print("📈 Simple Bullish Bias Detected");
        }
        else if(high_1 < high_5 && low_1 < low_5)
        {
            market_bias = -1; // Bearish
            Print("📉 Simple Bearish Bias Detected");
        }
    }

    // No signal if no clear market bias
    if(market_bias == 0)
        return 0;

    // Simplified entry logic - less restrictive for testing
    if(market_bias == 1) // Bullish bias
    {
        // Look for any bullish setup
        bool has_bullish_setup = false;

        // Check for bullish order block
        if(IsPriceAtBullishOB(current_price))
            has_bullish_setup = true;

        // Check for bullish FVG
        if(IsPriceAtBullishFVG(current_price))
            has_bullish_setup = true;

        // If no OB/FVG, use simple support level
        if(!has_bullish_setup)
        {
            double support = iLow(_Symbol, LTF_Timeframe, iLowest(_Symbol, LTF_Timeframe, MODE_LOW, 10, 1));
            if(current_price <= support + (5 * _Point * pip_multiplier))
                has_bullish_setup = true;
        }

        if(has_bullish_setup)
        {
            Print("🟢 SIMPLIFIED BUY SIGNAL: Bullish bias + Setup detected");
            return 1;
        }
    }

    if(market_bias == -1) // Bearish bias
    {
        // Look for any bearish setup
        bool has_bearish_setup = false;

        // Check for bearish order block
        if(IsPriceAtBearishOB(current_price))
            has_bearish_setup = true;

        // Check for bearish FVG
        if(IsPriceAtBearishFVG(current_price))
            has_bearish_setup = true;

        // If no OB/FVG, use simple resistance level
        if(!has_bearish_setup)
        {
            double resistance = iHigh(_Symbol, LTF_Timeframe, iHighest(_Symbol, LTF_Timeframe, MODE_HIGH, 10, 1));
            if(current_price >= resistance - (5 * _Point * pip_multiplier))
                has_bearish_setup = true;
        }

        if(has_bearish_setup)
        {
            Print("🔴 SIMPLIFIED SELL SIGNAL: Bearish bias + Setup detected");
            return -1;
        }
    }

    return 0;
}

//+------------------------------------------------------------------+
//| Backup Trading Signal (Simple Price Action)                    |
//+------------------------------------------------------------------+
int GetBackupTradingSignal()
{
    static datetime last_signal_time = 0;
    datetime current_time = TimeCurrent();

    // Prevent too frequent signals (minimum 5 minutes between signals)
    if(current_time - last_signal_time < 300)
        return 0;

    // Simple moving average crossover for backup signals
    double ma_fast = iMA(_Symbol, LTF_Timeframe, 5, 0, MODE_EMA, PRICE_CLOSE, 1);
    double ma_slow = iMA(_Symbol, LTF_Timeframe, 20, 0, MODE_EMA, PRICE_CLOSE, 1);
    double ma_fast_prev = iMA(_Symbol, LTF_Timeframe, 5, 0, MODE_EMA, PRICE_CLOSE, 2);
    double ma_slow_prev = iMA(_Symbol, LTF_Timeframe, 20, 0, MODE_EMA, PRICE_CLOSE, 2);

    double current_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);

    // Bullish crossover
    if(ma_fast > ma_slow && ma_fast_prev <= ma_slow_prev && current_price > ma_fast)
    {
        last_signal_time = current_time;
        Print("🟢 BACKUP BUY SIGNAL: MA Crossover + Price above fast MA");
        return 1;
    }

    // Bearish crossover
    if(ma_fast < ma_slow && ma_fast_prev >= ma_slow_prev && current_price < ma_fast)
    {
        last_signal_time = current_time;
        Print("🔴 BACKUP SELL SIGNAL: MA Crossover + Price below fast MA");
        return -1;
    }

    return 0;
}

//+------------------------------------------------------------------+
//| Check if price is at bullish order block                       |
//+------------------------------------------------------------------+
bool IsPriceAtBullishOB(double price)
{
    for(int i = 0; i < ArraySize(order_blocks); i++)
    {
        if(!order_blocks[i].is_valid || !order_blocks[i].is_bullish)
            continue;

        if(price >= order_blocks[i].low && price <= order_blocks[i].high)
        {
            Print("📦 Price at Bullish Order Block: ", order_blocks[i].low, " - ", order_blocks[i].high);
            return true;
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Check if price is at bearish order block                       |
//+------------------------------------------------------------------+
bool IsPriceAtBearishOB(double price)
{
    for(int i = 0; i < ArraySize(order_blocks); i++)
    {
        if(!order_blocks[i].is_valid || order_blocks[i].is_bullish)
            continue;

        if(price >= order_blocks[i].low && price <= order_blocks[i].high)
        {
            Print("📦 Price at Bearish Order Block: ", order_blocks[i].low, " - ", order_blocks[i].high);
            return true;
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Check if price is at bullish FVG                               |
//+------------------------------------------------------------------+
bool IsPriceAtBullishFVG(double price)
{
    for(int i = 0; i < ArraySize(fair_value_gaps); i++)
    {
        if(fair_value_gaps[i].is_filled || !fair_value_gaps[i].is_bullish)
            continue;

        if(price >= fair_value_gaps[i].lower && price <= fair_value_gaps[i].upper)
        {
            Print("⚡ Price at Bullish FVG: ", fair_value_gaps[i].lower, " - ", fair_value_gaps[i].upper);
            return true;
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Check if price is at bearish FVG                               |
//+------------------------------------------------------------------+
bool IsPriceAtBearishFVG(double price)
{
    for(int i = 0; i < ArraySize(fair_value_gaps); i++)
    {
        if(fair_value_gaps[i].is_filled || fair_value_gaps[i].is_bullish)
            continue;

        if(price >= fair_value_gaps[i].lower && price <= fair_value_gaps[i].upper)
        {
            Print("⚡ Price at Bearish FVG: ", fair_value_gaps[i].lower, " - ", fair_value_gaps[i].upper);
            return true;
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Check if liquidity was swept recently                          |
//+------------------------------------------------------------------+
bool IsLiquiditySweptRecently(bool check_buy_side)
{
    datetime recent_time = TimeCurrent() - PeriodSeconds(LTF_Timeframe) * 10; // Last 10 bars

    for(int i = 0; i < ArraySize(liquidity_levels); i++)
    {
        if(!liquidity_levels[i].is_swept)
            continue;

        if(liquidity_levels[i].time < recent_time)
            continue;

        if(check_buy_side && liquidity_levels[i].is_high)
            return true; // Buy-side liquidity swept

        if(!check_buy_side && !liquidity_levels[i].is_high)
            return true; // Sell-side liquidity swept
    }

    return false;
}

//+------------------------------------------------------------------+
//| Execute buy trade with SMC logic                               |
//+------------------------------------------------------------------+
void ExecuteBuyTrade()
{
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double lot_size = CalculateLotSize();

    if(lot_size <= 0)
    {
        Print("ERROR: Invalid lot size for buy trade");
        return;
    }

    double sl = NormalizeDouble(ask - (Stop_Loss_Pips * pip_multiplier * _Point), _Digits);
    double tp1 = NormalizeDouble(ask + (Stop_Loss_Pips * RR_Ratio_1 * pip_multiplier * _Point), _Digits);
    double tp2 = NormalizeDouble(ask + (Stop_Loss_Pips * RR_Ratio_2 * pip_multiplier * _Point), _Digits);
    double tp3 = NormalizeDouble(ask + (Stop_Loss_Pips * RR_Ratio_3 * pip_multiplier * _Point), _Digits);

    // Validate stop levels
    if(!ValidateStopLevels(ask, sl, tp1, true))
        return;

    if(trade.Buy(lot_size, _Symbol, ask, sl, tp1, "SMC BUY"))
    {
        ulong ticket = trade.ResultDeal();
        if(ticket > 0)
        {
            Print("✅ SMC BUY EXECUTED: Ticket=", ticket, " Lot=", lot_size);
            Print("📊 Entry=", ask, " SL=", sl, " TP1=", tp1, " TP2=", tp2, " TP3=", tp3);
            Print("💰 Risk: ", Risk_Percent, "% | RR: 1:", RR_Ratio_1, " | Bias: BULLISH");
        }
    }
    else
    {
        Print("❌ Failed to execute SMC BUY: ", trade.ResultRetcodeDescription());
    }
}

//+------------------------------------------------------------------+
//| Execute sell trade with SMC logic                              |
//+------------------------------------------------------------------+
void ExecuteSellTrade()
{
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double lot_size = CalculateLotSize();

    if(lot_size <= 0)
    {
        Print("ERROR: Invalid lot size for sell trade");
        return;
    }

    double sl = NormalizeDouble(bid + (Stop_Loss_Pips * pip_multiplier * _Point), _Digits);
    double tp1 = NormalizeDouble(bid - (Stop_Loss_Pips * RR_Ratio_1 * pip_multiplier * _Point), _Digits);
    double tp2 = NormalizeDouble(bid - (Stop_Loss_Pips * RR_Ratio_2 * pip_multiplier * _Point), _Digits);
    double tp3 = NormalizeDouble(bid - (Stop_Loss_Pips * RR_Ratio_3 * pip_multiplier * _Point), _Digits);

    // Validate stop levels
    if(!ValidateStopLevels(bid, sl, tp1, false))
        return;

    if(trade.Sell(lot_size, _Symbol, bid, sl, tp1, "SMC SELL"))
    {
        ulong ticket = trade.ResultDeal();
        if(ticket > 0)
        {
            Print("✅ SMC SELL EXECUTED: Ticket=", ticket, " Lot=", lot_size);
            Print("📊 Entry=", bid, " SL=", sl, " TP1=", tp1, " TP2=", tp2, " TP3=", tp3);
            Print("💰 Risk: ", Risk_Percent, "% | RR: 1:", RR_Ratio_1, " | Bias: BEARISH");
        }
    }
    else
    {
        Print("❌ Failed to execute SMC SELL: ", trade.ResultRetcodeDescription());
    }
}

//+------------------------------------------------------------------+
//| Calculate lot size based on risk percentage                     |
//+------------------------------------------------------------------+
double CalculateLotSize()
{
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double risk_amount = balance * Risk_Percent / 100.0;
    double pip_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);

    if(pip_value == 0)
        pip_value = 1.0; // Fallback

    double lot_size = risk_amount / (Stop_Loss_Pips * pip_value * pip_multiplier);

    // Normalize lot size
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    lot_size = MathMax(min_lot, MathMin(max_lot, lot_size));
    lot_size = NormalizeDouble(lot_size / lot_step, 0) * lot_step;

    // Check margin requirements
    double free_margin = AccountInfoDouble(ACCOUNT_MARGIN_FREE);
    double required_margin = lot_size * SymbolInfoDouble(_Symbol, SYMBOL_MARGIN_INITIAL);

    if(required_margin > free_margin * 0.8)
    {
        lot_size = (free_margin * 0.8) / SymbolInfoDouble(_Symbol, SYMBOL_MARGIN_INITIAL);
        lot_size = NormalizeDouble(lot_size / lot_step, 0) * lot_step;
        lot_size = MathMax(min_lot, lot_size);
    }

    return lot_size;
}

//+------------------------------------------------------------------+
//| Validate stop levels                                             |
//+------------------------------------------------------------------+
bool ValidateStopLevels(double entry, double sl, double tp, bool is_buy)
{
    double min_stop_level = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL) * _Point;

    if(is_buy)
    {
        if((entry - sl) < min_stop_level || (tp - entry) < min_stop_level)
        {
            Print("ERROR: Stop levels too close for BUY. Min distance: ", min_stop_level/_Point, " points");
            return false;
        }
    }
    else
    {
        if((sl - entry) < min_stop_level || (entry - tp) < min_stop_level)
        {
            Print("ERROR: Stop levels too close for SELL. Min distance: ", min_stop_level/_Point, " points");
            return false;
        }
    }

    return true;
}

//+------------------------------------------------------------------+
//| Manage open trades with SMC logic                              |
//+------------------------------------------------------------------+
void ManageOpenTrades()
{
    int total_positions = PositionsTotal();

    for(int i = 0; i < total_positions; i++)
    {
        if(PositionGetTicket(i) == 0) continue;
        if(PositionGetInteger(POSITION_MAGIC) != Magic_Number) continue;
        if(PositionGetString(POSITION_SYMBOL) != _Symbol) continue;

        ulong ticket = PositionGetInteger(POSITION_TICKET);
        double entry_price = PositionGetDouble(POSITION_PRICE_OPEN);
        double current_sl = PositionGetDouble(POSITION_SL);
        double current_tp = PositionGetDouble(POSITION_TP);
        bool is_buy = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY);
        double current_volume = PositionGetDouble(POSITION_VOLUME);

        double current_price = is_buy ? SymbolInfoDouble(_Symbol, SYMBOL_BID) : SymbolInfoDouble(_Symbol, SYMBOL_ASK);
        double profit_pips = is_buy ? (current_price - entry_price) : (entry_price - current_price);
        profit_pips = profit_pips / (_Point * pip_multiplier);

        // Multi-TP management
        if(Use_Multi_TP)
        {
            double tp1_pips = Stop_Loss_Pips * RR_Ratio_1;
            double tp2_pips = Stop_Loss_Pips * RR_Ratio_2;

            // TP1 - Partial close
            if(profit_pips >= tp1_pips && !GlobalVariableCheck("TP1_" + IntegerToString(ticket)))
            {
                double close_volume = NormalizeDouble(current_volume * TP1_Close_Percent / 100.0, 2);
                if(close_volume >= SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN))
                {
                    if(trade.PositionClosePartial(ticket, close_volume))
                    {
                        GlobalVariableSet("TP1_" + IntegerToString(ticket), 1);
                        Print("🎯 TP1 Hit: ", TP1_Close_Percent, "% closed at ", tp1_pips, " pips");

                        // Move SL to breakeven
                        if(Move_SL_to_BE)
                        {
                            double new_sl = NormalizeDouble(entry_price, _Digits);
                            trade.PositionModify(ticket, new_sl, current_tp);
                            Print("🔒 SL moved to breakeven");
                        }
                    }
                }
            }

            // TP2 - Partial close
            if(profit_pips >= tp2_pips && !GlobalVariableCheck("TP2_" + IntegerToString(ticket)))
            {
                double close_volume = NormalizeDouble(current_volume * TP2_Close_Percent / 100.0, 2);
                if(close_volume >= SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN))
                {
                    if(trade.PositionClosePartial(ticket, close_volume))
                    {
                        GlobalVariableSet("TP2_" + IntegerToString(ticket), 1);
                        Print("🎯 TP2 Hit: ", TP2_Close_Percent, "% closed at ", tp2_pips, " pips");
                    }
                }
            }
        }

        // Trailing stop after TP2
        if(Use_Trailing_Stop && profit_pips >= Trail_Start_Pips)
        {
            double trail_distance = Trail_Step_Pips * pip_multiplier * _Point;
            double new_sl;

            if(is_buy)
            {
                new_sl = NormalizeDouble(current_price - trail_distance, _Digits);
                if(new_sl > current_sl)
                {
                    if(trade.PositionModify(ticket, new_sl, current_tp))
                    {
                        Print("📈 Trailing SL: ", new_sl, " (", Trail_Step_Pips, " pips trail)");
                    }
                }
            }
            else
            {
                new_sl = NormalizeDouble(current_price + trail_distance, _Digits);
                if(new_sl < current_sl)
                {
                    if(trade.PositionModify(ticket, new_sl, current_tp))
                    {
                        Print("📉 Trailing SL: ", new_sl, " (", Trail_Step_Pips, " pips trail)");
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Close all open trades                                            |
//+------------------------------------------------------------------+
void CloseAllTrades()
{
    int total_positions = PositionsTotal();

    for(int i = total_positions - 1; i >= 0; i--)
    {
        if(PositionGetTicket(i) == 0) continue;
        if(PositionGetInteger(POSITION_MAGIC) != Magic_Number) continue;
        if(PositionGetString(POSITION_SYMBOL) != _Symbol) continue;

        ulong ticket = PositionGetInteger(POSITION_TICKET);
        if(trade.PositionClose(ticket))
        {
            Print("🔒 Position closed due to daily limit: ", ticket);
        }
    }
}

//+------------------------------------------------------------------+
//| Additional SMC Helper Functions                                 |
//+------------------------------------------------------------------+

// Update Liquidity Levels
void UpdateLiquidityLevels()
{
    // Find equal highs and equal lows for liquidity
    int highs_count = ArraySize(swing_highs);
    int lows_count = ArraySize(swing_lows);

    // Check for equal highs (buy-side liquidity)
    for(int i = 0; i < highs_count - 1; i++)
    {
        for(int j = i + 1; j < highs_count; j++)
        {
            double price_diff = MathAbs(swing_highs[i].price - swing_highs[j].price) / (_Point * pip_multiplier);
            if(price_diff <= Liquidity_Buffer_Pips)
            {
                LiquidityLevel liq;
                liq.price = (swing_highs[i].price + swing_highs[j].price) / 2.0;
                liq.time = MathMax(swing_highs[i].time, swing_highs[j].time);
                liq.is_swept = false;
                liq.touch_count = 2;
                liq.is_high = true;

                AddLiquidityLevel(liq);
            }
        }
    }

    // Check for equal lows (sell-side liquidity)
    for(int i = 0; i < lows_count - 1; i++)
    {
        for(int j = i + 1; j < lows_count; j++)
        {
            double price_diff = MathAbs(swing_lows[i].price - swing_lows[j].price) / (_Point * pip_multiplier);
            if(price_diff <= Liquidity_Buffer_Pips)
            {
                LiquidityLevel liq;
                liq.price = (swing_lows[i].price + swing_lows[j].price) / 2.0;
                liq.time = MathMax(swing_lows[i].time, swing_lows[j].time);
                liq.is_swept = false;
                liq.touch_count = 2;
                liq.is_high = false;

                AddLiquidityLevel(liq);
            }
        }
    }

    // Check if liquidity levels are swept
    CheckLiquiditySweeps();
}

void AddLiquidityLevel(LiquidityLevel &liq)
{
    // Check if similar level already exists
    for(int i = 0; i < ArraySize(liquidity_levels); i++)
    {
        double price_diff = MathAbs(liquidity_levels[i].price - liq.price) / (_Point * pip_multiplier);
        if(price_diff <= Liquidity_Buffer_Pips)
            return; // Similar level exists
    }

    int size = ArraySize(liquidity_levels);
    ArrayResize(liquidity_levels, size + 1);
    liquidity_levels[size] = liq;

    Print("💧 New Liquidity Level: ", liq.is_high ? "HIGH" : "LOW", " at ", liq.price);
}

void CheckLiquiditySweeps()
{
    double current_high = iHigh(_Symbol, LTF_Timeframe, 0);
    double current_low = iLow(_Symbol, LTF_Timeframe, 0);

    for(int i = 0; i < ArraySize(liquidity_levels); i++)
    {
        if(liquidity_levels[i].is_swept)
            continue;

        if(liquidity_levels[i].is_high && current_high > liquidity_levels[i].price)
        {
            liquidity_levels[i].is_swept = true;
            liquidity_levels[i].time = TimeCurrent();
            Print("💥 Buy-side liquidity SWEPT at ", liquidity_levels[i].price);
        }
        else if(!liquidity_levels[i].is_high && current_low < liquidity_levels[i].price)
        {
            liquidity_levels[i].is_swept = true;
            liquidity_levels[i].time = TimeCurrent();
            Print("💥 Sell-side liquidity SWEPT at ", liquidity_levels[i].price);
        }
    }
}

void CalculatePremiumDiscount()
{
    int highs_count = ArraySize(swing_highs);
    int lows_count = ArraySize(swing_lows);

    if(highs_count > 0 && lows_count > 0)
    {
        double recent_high = swing_highs[highs_count-1].price;
        double recent_low = swing_lows[lows_count-1].price;
        premium_discount_line = (recent_high + recent_low) / 2.0;
    }
}

void CheckForCHoCH()
{
    // Simplified CHoCH detection - look for internal structure breaks
    double high[], low[];

    if(CopyHigh(_Symbol, LTF_Timeframe, 0, 10, high) < 10 ||
       CopyLow(_Symbol, LTF_Timeframe, 0, 10, low) < 10)
        return;

    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);

    // Check for break of recent structure
    if(market_bias == 1) // Bullish bias
    {
        if(low[0] < low[5]) // Recent low broken
        {
            choch_confirmed = true;
            last_choch_time = TimeCurrent();
            Print("🔄 CHoCH Confirmed: Bullish structure break");
        }
    }
    else if(market_bias == -1) // Bearish bias
    {
        if(high[0] > high[5]) // Recent high broken
        {
            choch_confirmed = true;
            last_choch_time = TimeCurrent();
            Print("🔄 CHoCH Confirmed: Bearish structure break");
        }
    }
}

void CheckFVGFilled()
{
    double current_high = iHigh(_Symbol, LTF_Timeframe, 0);
    double current_low = iLow(_Symbol, LTF_Timeframe, 0);

    for(int i = 0; i < ArraySize(fair_value_gaps); i++)
    {
        if(fair_value_gaps[i].is_filled)
            continue;

        // Check if FVG is filled
        if(current_low <= fair_value_gaps[i].lower && current_high >= fair_value_gaps[i].upper)
        {
            fair_value_gaps[i].is_filled = true;
            Print("⚡ FVG FILLED: ", fair_value_gaps[i].is_bullish ? "Bullish" : "Bearish");
        }
    }
}

void CleanupOrderBlocks()
{
    datetime cutoff_time = TimeCurrent() - PeriodSeconds(LTF_Timeframe) * OB_Validity_Bars;

    for(int i = ArraySize(order_blocks) - 1; i >= 0; i--)
    {
        if(order_blocks[i].time < cutoff_time)
        {
            // Remove old order block
            for(int j = i; j < ArraySize(order_blocks) - 1; j++)
                order_blocks[j] = order_blocks[j + 1];
            ArrayResize(order_blocks, ArraySize(order_blocks) - 1);
        }
    }
}

void RemoveChartObjects()
{
    // Remove all SMC-related chart objects
    ObjectsDeleteAll(0, "SMC_");
}

//+------------------------------------------------------------------+
//| Update SMC Dashboard                                            |
//+------------------------------------------------------------------+
void UpdateDashboard()
{
    string dashboard_text = "";

    // EA Info
    dashboard_text += "🧠 SMART MONEY CONCEPTS EA v2.0\n";
    dashboard_text += "Symbol: " + _Symbol + " | Magic: " + IntegerToString(Magic_Number) + "\n";
    dashboard_text += "═══════════════════════════════\n";

    // Account Info
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double equity = AccountInfoDouble(ACCOUNT_EQUITY);
    double free_margin = AccountInfoDouble(ACCOUNT_MARGIN_FREE);

    dashboard_text += "💰 Balance: $" + DoubleToString(balance, 2) + "\n";
    dashboard_text += "💎 Equity: $" + DoubleToString(equity, 2) + "\n";
    dashboard_text += "📊 Free Margin: $" + DoubleToString(free_margin, 2) + "\n";
    dashboard_text += "═══════════════════════════════\n";

    // Daily Performance
    dashboard_text += "📈 Daily Profit: $" + DoubleToString(daily_profit, 2) + " / $" + DoubleToString(Daily_Profit_Target, 2) + "\n";
    dashboard_text += "📉 Daily Loss: $" + DoubleToString(daily_loss, 2) + "\n";
    dashboard_text += "🎯 Daily Trades: " + IntegerToString(daily_trades) + " / " + IntegerToString(Max_Daily_Trades) + "\n";
    dashboard_text += "═══════════════════════════════\n";

    // SMC Analysis
    string bias_text = "NEUTRAL";
    if(market_bias == 1) bias_text = "BULLISH 🟢";
    else if(market_bias == -1) bias_text = "BEARISH 🔴";

    dashboard_text += "🧠 Market Bias: " + bias_text + "\n";
    dashboard_text += "🔄 CHoCH: " + (choch_confirmed ? "CONFIRMED ✅" : "PENDING ⏳") + "\n";
    dashboard_text += "📦 Order Blocks: " + IntegerToString(ArraySize(order_blocks)) + "\n";
    dashboard_text += "⚡ FVGs: " + IntegerToString(ArraySize(fair_value_gaps)) + "\n";
    dashboard_text += "💧 Liquidity Levels: " + IntegerToString(ArraySize(liquidity_levels)) + "\n";
    dashboard_text += "⚖️ Premium/Discount: " + DoubleToString(premium_discount_line, _Digits) + "\n";
    dashboard_text += "═══════════════════════════════\n";

    // Trading Status
    dashboard_text += "⏰ Session: " + (IsWithinTradingSession() ? "ACTIVE 🟢" : "CLOSED 🔴") + "\n";
    dashboard_text += "📊 Spread: " + DoubleToString((SymbolInfoDouble(_Symbol, SYMBOL_ASK) - SymbolInfoDouble(_Symbol, SYMBOL_BID)) / _Point / pip_multiplier, 1) + " pips\n";
    dashboard_text += "🎯 Target Reached: " + (daily_target_reached ? "YES ✅" : "NO ❌") + "\n";
    dashboard_text += "🚫 Loss Exceeded: " + (daily_loss_exceeded ? "YES ⚠️" : "NO ✅") + "\n";
    dashboard_text += "═══════════════════════════════\n";

    // Strategy Info
    dashboard_text += "⚙️ Strategy: Smart Money Concepts\n";
    dashboard_text += "🎯 SL: " + IntegerToString(Stop_Loss_Pips) + " pips | RR: 1:" + DoubleToString(RR_Ratio_1, 1) + "\n";
    dashboard_text += "📈 Risk: " + DoubleToString(Risk_Percent, 1) + "% per trade\n";
    dashboard_text += "📊 HTF: " + EnumToString(HTF_Timeframe) + " | LTF: " + EnumToString(LTF_Timeframe) + "\n";

    // Current positions
    int open_positions = 0;
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionGetTicket(i) == 0) continue;
        if(PositionGetInteger(POSITION_MAGIC) != Magic_Number) continue;
        if(PositionGetString(POSITION_SYMBOL) != _Symbol) continue;
        open_positions++;
    }
    dashboard_text += "📊 Open Positions: " + IntegerToString(open_positions) + "\n";

    // Create or update dashboard object
    ObjectDelete(0, "SMC_Dashboard");
    ObjectCreate(0, "SMC_Dashboard", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "SMC_Dashboard", OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, "SMC_Dashboard", OBJPROP_XDISTANCE, Dashboard_X);
    ObjectSetInteger(0, "SMC_Dashboard", OBJPROP_YDISTANCE, Dashboard_Y);
    ObjectSetInteger(0, "SMC_Dashboard", OBJPROP_COLOR, Dashboard_Color);
    ObjectSetInteger(0, "SMC_Dashboard", OBJPROP_FONTSIZE, 9);
    ObjectSetString(0, "SMC_Dashboard", OBJPROP_FONT, "Courier New");
    ObjectSetString(0, "SMC_Dashboard", OBJPROP_TEXT, dashboard_text);
}

//+------------------------------------------------------------------+
//| Remove dashboard objects                                         |
//+------------------------------------------------------------------+
void RemoveDashboard()
{
    ObjectDelete(0, "SMC_Dashboard");
}

//+------------------------------------------------------------------+
//| OnTrade event handler                                            |
//+------------------------------------------------------------------+
void OnTrade()
{
    // Update statistics when trades are closed
    UpdateDailyStats();

    // Clean up global variables for closed positions
    CleanupClosedPositions();
}

//+------------------------------------------------------------------+
//| Clean up global variables for closed positions                  |
//+------------------------------------------------------------------+
void CleanupClosedPositions()
{
    // Clean up global variables for closed positions
    int total_vars = GlobalVariablesTotal();
    for(int i = total_vars - 1; i >= 0; i--)
    {
        string var_name = GlobalVariableName(i);
        if(StringFind(var_name, "TP1_") == 0 || StringFind(var_name, "TP2_") == 0 ||
           StringFind(var_name, "BreakevenSet_") == 0)
        {
            string ticket_str = StringSubstr(var_name, StringFind(var_name, "_") + 1);
            ulong ticket = (ulong)StringToInteger(ticket_str);

            // Check if position still exists
            bool position_exists = false;
            for(int j = 0; j < PositionsTotal(); j++)
            {
                if(PositionGetTicket(j) == ticket)
                {
                    position_exists = true;
                    break;
                }
            }

            if(!position_exists)
            {
                GlobalVariableDel(var_name);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| SMC Strategy Summary                                             |
//+------------------------------------------------------------------+
/*
🧠 SMART MONEY CONCEPTS EA v2.0 - STRATEGY OVERVIEW

📊 MARKET STRUCTURE ANALYSIS:
- Higher Timeframe (1H/4H): Determines market bias (bullish/bearish)
- Swing Highs/Lows: Identifies key structure points
- Break of Structure (BoS): Confirms trend continuation
- Change of Character (CHoCH): Signals potential reversal

💧 LIQUIDITY CONCEPTS:
- Equal Highs/Lows: Identifies liquidity pools
- Liquidity Sweeps: Confirms smart money manipulation
- Stop Hunt Detection: Looks for false breakouts

📦 ORDER BLOCKS:
- Bullish OB: Last bearish candle before bullish displacement
- Bearish OB: Last bullish candle before bearish displacement
- Mitigation: Price returns to OB for institutional entry

⚡ FAIR VALUE GAPS (FVG):
- Bullish FVG: Gap between candle 1 low and candle 3 high
- Bearish FVG: Gap between candle 1 high and candle 3 low
- Rebalance: Price returns to fill the gap

⚖️ PREMIUM/DISCOUNT ZONES:
- Above 50%: Premium zone (look for sells)
- Below 50%: Discount zone (look for buys)
- Equilibrium: 50% level between swing high/low

🎯 ENTRY CRITERIA:
BUY SIGNAL:
1. Bullish market bias (HH + HL)
2. Price in discount zone (<50%)
3. Sell-side liquidity swept
4. CHoCH confirmed (if enabled)
5. Price at bullish OB or FVG

SELL SIGNAL:
1. Bearish market bias (LH + LL)
2. Price in premium zone (>50%)
3. Buy-side liquidity swept
4. CHoCH confirmed (if enabled)
5. Price at bearish OB or FVG

💰 RISK MANAGEMENT:
- Risk: 0.5-1% per trade
- Stop Loss: 5 pips (tight)
- Take Profit: Multiple levels (1:1, 1:2, 1:3)
- Partial Closes: 30% at TP1, 40% at TP2
- Breakeven: After TP1 hit
- Trailing Stop: After TP2 hit

⏰ TRADING SESSIONS:
- London: 8:00-11:00 GMT
- New York: 13:00-16:00 GMT
- Avoid: Asian session & high impact news

🎯 DAILY TARGETS (for $50 account):
- Profit Target: $5-8 per day (10-16% growth)
- Max Loss: 2% of account
- Max Trades: 10 per day
- Win Rate Target: 70%+

This EA implements institutional trading logic used by banks and hedge funds,
focusing on high-probability setups with tight risk management suitable for
small accounts aiming for consistent daily profits.
*/
