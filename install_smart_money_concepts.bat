@echo off
echo ================================================================
echo 🧠 SMART MONEY CONCEPTS EA v2.0 - INSTALLATION SCRIPT
echo ================================================================
echo.

REM Get MetaTrader 5 installation path
set "MT5_PATH=%APPDATA%\MetaQuotes\Terminal"

REM Check if MT5 directory exists
if not exist "%MT5_PATH%" (
    echo ❌ MetaTrader 5 not found in default location.
    echo Please install MetaTrader 5 first or check installation path.
    pause
    exit /b 1
)

echo 📁 MetaTrader 5 found at: %MT5_PATH%
echo.

REM Find the terminal hash folder (latest one)
for /f "delims=" %%i in ('dir "%MT5_PATH%" /b /ad /od') do set "TERMINAL_HASH=%%i"

if "%TERMINAL_HASH%"=="" (
    echo ❌ No terminal data folder found.
    echo Please run MetaTrader 5 at least once to create data folders.
    pause
    exit /b 1
)

set "MQL5_PATH=%MT5_PATH%\%TERMINAL_HASH%\MQL5"
echo 📂 Using terminal: %TERMINAL_HASH%
echo 📂 MQL5 path: %MQL5_PATH%
echo.

REM Create directories if they don't exist
if not exist "%MQL5_PATH%\Experts" mkdir "%MQL5_PATH%\Experts"
if not exist "%MQL5_PATH%\Presets" mkdir "%MQL5_PATH%\Presets"

echo 📋 Installing Smart Money Concepts EA...
echo.

REM Copy EA file
if exist "PrecisionScalpingEA.mq5" (
    copy "PrecisionScalpingEA.mq5" "%MQL5_PATH%\Experts\SmartMoneyConceptsEA.mq5" >nul
    if %errorlevel%==0 (
        echo ✅ SmartMoneyConceptsEA.mq5 installed successfully
    ) else (
        echo ❌ Failed to copy EA file
    )
) else (
    echo ❌ PrecisionScalpingEA.mq5 not found in current directory
)

REM Copy settings file
if exist "SmartMoneyConceptsEA.set" (
    copy "SmartMoneyConceptsEA.set" "%MQL5_PATH%\Presets\SmartMoneyConceptsEA.set" >nul
    if %errorlevel%==0 (
        echo ✅ SmartMoneyConceptsEA.set installed successfully
    ) else (
        echo ❌ Failed to copy settings file
    )
) else (
    echo ❌ SmartMoneyConceptsEA.set not found in current directory
)

REM Copy README file
if exist "SmartMoneyConcepts_README.txt" (
    copy "SmartMoneyConcepts_README.txt" "%MQL5_PATH%\SmartMoneyConcepts_README.txt" >nul
    if %errorlevel%==0 (
        echo ✅ README file installed successfully
    ) else (
        echo ❌ Failed to copy README file
    )
) else (
    echo ❌ SmartMoneyConcepts_README.txt not found in current directory
)

echo.
echo ================================================================
echo 🎯 INSTALLATION COMPLETE!
echo ================================================================
echo.
echo 📋 NEXT STEPS:
echo 1. Restart MetaTrader 5
echo 2. Open a chart (EURUSD, GBPUSD, or XAUUSD recommended)
echo 3. Drag SmartMoneyConceptsEA from Navigator to chart
echo 4. Load the SmartMoneyConceptsEA.set preset
echo 5. Enable AutoTrading (Ctrl+E)
echo 6. Monitor the dashboard for performance
echo.
echo 📖 IMPORTANT:
echo - Read SmartMoneyConcepts_README.txt for complete guide
echo - Test on demo account first
echo - Use during London (8-11 GMT) or NY (13-16 GMT) sessions
echo - Risk only 0.5-1%% per trade
echo - Target: $5-8 daily profit for $50 account
echo.
echo 🧠 SMART MONEY CONCEPTS FEATURES:
echo ✅ Order Blocks detection
echo ✅ Fair Value Gaps identification
echo ✅ Liquidity sweeps confirmation
echo ✅ Change of Character (CHoCH) analysis
echo ✅ Premium/Discount zones
echo ✅ Multi-TP with trailing stop
echo ✅ Comprehensive dashboard
echo.
echo Happy Trading! 🚀
echo.
pause
